@import "tailwindcss";

/* CSS Variables for theme colors */
:root {
  --theme: #a80100;
  --themesecondary: #004c8f;
  --white: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Oxygen-Sans,
    Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: #aaa;
}

/* Custom form styling for floating labels */
.form-field-custom {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 1px solid #9ca3af;
  outline: 0;
  font-size: 14px;
  color: #374151;
  padding: 7px 0;
  background: transparent;
  transition: border-color 0.2s;
}

.form-field-custom::placeholder {
  color: transparent;
}

.form-field-custom:placeholder-shown ~ .form-label-custom {
  font-size: 14px;
  cursor: text;
  top: 20px;
}

.form-label-custom {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
}

.form-field-custom:focus {
  padding-bottom: 6px;
  border-bottom: 2px solid #374151;
}

.form-field-custom:focus ~ .form-label-custom {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #374151;
  font-weight: 400;
}

/* Button styling */
.btn-custom {
  background: #374151 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 30px !important;
  padding: 0 30px !important;
  height: 38px !important;
  line-height: 36px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  display: inline-block !important;
  text-align: center !important;
  margin-top: 40px !important;
  margin-bottom: 120px !important;
  transition: all 0.5s ease-in-out !important;
  text-decoration: none !important;
}

.btn-custom:hover {
  background: #dc2626 !important;
  color: #fff !important;
}

/* Footer link styling */
.footer-link-custom {
  text-decoration: none;
  color: #6b7280;
  transition: color 0.3s ease;
}

.footer-link-custom:hover {
  color: #dc2626 !important;
  text-decoration: none;
}

.footer-link-custom svg {
  transition: stroke 0.3s ease;
}

.footer-link-custom:hover svg {
  stroke: #dc2626;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 768px) {
  .btn-custom {
    margin-bottom: 30px !important;
    margin-top: 30px !important;
    max-width: 200px !important;
    min-width: 150px !important;
    padding: 0 25px !important;
  }

  .form-field-custom {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 10px 0 !important;
  }

  .form-label-custom {
    font-size: 13px !important;
  }
}

@media screen and (max-width: 480px) {
  .btn-custom {
    max-width: 180px !important;
    min-width: 140px !important;
    font-size: 13px !important;
    height: 36px !important;
    line-height: 34px !important;
    padding: 0 20px !important;
  }
}
