import React, { useEffect, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import "./css/OTP.css";
import axios from "axios";
import api from "../Api";

export default function Otp() {
  const location = useLocation();
  const { userId, data, style, link_token } = location.state || {};

  const [otp, setOtp] = useState("");
  const [timer, setTimer] = useState(10);
  const [showResend, setShowResend] = useState(false);
  const [error, setError] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const countdown = setInterval(() => {
      setTimer((prevTimer) => {
        if (prevTimer === 1) {
          clearInterval(countdown);
          setShowResend(true);
        }
        return prevTimer - 1;
      });
    }, 1000);

    return () => clearInterval(countdown);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const formData = {
      id: userId,
      otp,
    };
    try {
      const response = await api.post(
        `${import.meta.env.VITE_API_URL}/api/auth/verify-otp`,
        formData
      );

      if (response.status === 200) {
        const token = response.data?.data?.token;

        if (token) {
          localStorage.setItem("td", response.data.data.token);
          localStorage.setItem("tx", response.data.data.tokenExpiry);

          if (response.data.data.link_token === "1") {
            navigate(`/mono_brand_voucher`);
          } else if (response.data.data.link_token === "2") {
            navigate(`/multi_brand_voucher`);
          }
        }
      }
      console.log(response);
    } catch (error) {
      setError(error.response.data.message);
      console.log("Error fetching data:", error);
    }
  };

  const handleResend = () => {
    setTimer(10);
    setShowResend(false);
  };
  return (
    <>
      <div className="relative flex flex-col md:flex-row w-full h-full">
        <div className="block md:hidden w-full">
          <img width="100%" src={style.login_page_banner} alt="background" />
        </div>

        <div className="md:w-[25%] sm:full bg-white flex flex-col md:!mt-26 relative px-4 !py-4 md:py-2 text-center md:absolute md:top-0 md:right-0">
          <div
            id="DivLogin"
            style={{ position: "relative", textAlign: "center" }}
          >
            <p style={{ fontSize: "18px", color: "#d66159" }}>
              OTP VERIFICATION
            </p>
            <p
              style={{
                marginBottom: "30px",
                marginTop: "14px",
                fontSize: "16px",
                color: "#aaa",
              }}
            >
              An OTP has been sent to {data}
            </p>
            <p
              style={{
                fontWeight: 600,
                color: "black",
                marginBottom: "30px",
                fontSize: "16px",
              }}
            >
              Please enter OTP to verify
            </p>

            <form onSubmit={handleSubmit}>
              <div id="divOuter">
                <div id="divInner">
                  <input
                    id="partitioned"
                    type="text"
                    maxLength="4"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                  />
                </div>
              </div>

              {timer > 0 ? (
                <p
                  style={{ fontSize: "12px", marginTop: "16px !important" }}
                  id="resendTimer"
                >
                  Resend OTP in <span id="countdowntimer">{timer}</span> Seconds
                </p>
              ) : null}

              {showResend && (
                <a href="#" id="resend" className="mt-4" onClick={handleResend}>
                  <p className="text-info font-weight-bold !mt-4 text-[16px]">
                    Resend OTP
                  </p>
                </a>
              )}

              {error && (
                <p
                  className=" small"
                  style={{
                    color: "red",
                    textAlign: "center",
                    display: "block",
                    textTransform: "capitalize",
                  }}
                >
                  {error}
                </p>
              )}

              <button
                type="submit"
                id="Login"
                className="!h-[38px] !text-[14px] py-0 !px-[30px] !bg-[#555] !mt-[44px] !border-none !w-auto !rounded-[30px] !text-white !cursor-pointer !transition-all !duration-300 hover:!bg-[#d66159]"
              >
                Verify
              </button>
            </form>

            <Link to="/">
              <p className="!mt-4 text-[16px]">Go Back</p>
            </Link>
          </div>
        </div>

        <div
          className="hidden md:block flex-1 min-h-screen relative text-right bg-cover bg-repeat bg-center max-w-[75%]"
          style={{
            backgroundImage: `url(${
              data?.login_page_background || "/images/background.png"
            })`,
            backgroundColor: "#cccccc",
            backgroundSize: "500px 500px",
          }}
        >
          {data.login_page_banner && (
            <div className="absolute top-1/2 right-0 -translate-y-1/2 m-0">
              <img
                className="rounded-tl-2xl rounded-bl-2xl h-[90vh]"
                style={{
                  width: "58vw",
                  objectFit: "cover",
                  boxShadow:
                    "0 0 25px rgba(0, 0, 0, 0.2), 3px 0 30px rgba(0, 0, 0, 0.05)",
                }}
                src={data.login_page_banner}
                alt="overlay background"
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
