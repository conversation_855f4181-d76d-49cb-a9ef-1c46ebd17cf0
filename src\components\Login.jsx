import React, { useEffect, useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import CryptoJS from "crypto-js";
const SECRET_KEY = "your_secret_key";
import api from "../Api.jsx";
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

export default function Login() {
  const [voucherCode, setVoucherCode] = useState("");
  const [contactInfo, setContactInfo] = useState("");
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState("");
  const [data, setData] = useState("");
  const navigate = useNavigate();

  // call program setting  api

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const v = params.get("v");
    const p = params.get("p");
    localStorage.setItem("p", p);
    {
      v && setVoucherCode(v);
    }

    const fetchData = async () => {
      try {
        const response = await api.get(
          `${
            import.meta.env.VITE_API_URL
          }/api/auth/get_program_settings?pid=${p}`
        );
        {
          response.status === 200 && setData(response.data.data.settings);
        }
        // console.log(response);
        //   const data = await response.json();
        //   setApiData(data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!voucherCode) {
      setError("Please Enter a valid Voucher Code");
      return;
    }
    const encryptedToken = encryptData(voucherCode);
    localStorage.setItem("token", encryptedToken);

    if (!contactInfo) {
      if (data.voucher_link_authentication_mode === 3) {
        setError("Please Enter Your Pin");
        return;
      } else if (data.voucher_link_authentication_mode === 2) {
        setError("Please Enter  Your  Email");
        return;
      } else {
        setError("Please Enter Your Mobile Number");
        return;
      }
    }

    let contactFieldName = "mobile";
    // data.voucher_link_authentication_mode
    switch (data.voucher_link_authentication_mode) {
      case 2:
        contactFieldName = "email";
        break;
      case 3:
        contactFieldName = "pin";
        break;
      default:
        contactFieldName = "mobile";
    }

    const formData = {
      voucher_code: voucherCode,
      [contactFieldName]: contactInfo,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/auth/login`,
        formData
      );

      if (response.status === 200) {
        const tokenData = response.data?.data?.tokenData;
        console.log(tokenData);

        if (tokenData) {
          localStorage.setItem("tx", tokenData.tokenExpiry);
          localStorage.setItem("td", tokenData.token);

          if (response.data.data.link_token === "1") {
            navigate(`/mono_brand_voucher`);
          } else if (response.data.data.link_token === "2") {
            navigate(`/multi_brand_voucher`);
          }
        } else {
          navigate("/otp", {
            state: {
              userId: response.data.data.user_id,
              data: contactInfo,
              style: data,
              link_token: response.data.data.link_token,
            },
          });
        }
      }
    } catch (error) {
      setError(error.response.data.message);
      console.log("Error fetching data:", error);
    }
  };

  // field acc to auth mode

  const getInputDetails = () => {
    switch (data.voucher_link_authentication_mode) {
      case 1:
        return {
          inputType: "tel",
          label: "Mobile Number",
          placeholder: "Enter Mobile Number",
        };
      case 2:
        return {
          inputType: "email",
          label: "Email Address",
          placeholder: "Enter Email Address",
        };
      case 3:
        return {
          inputType: "text",
          label: "PIN",
          placeholder: "Enter PIN",
        };
      default:
        return {
          inputType: "text",
          label: "Contact Information",
          placeholder: "Enter Information",
        };
    }
  };
  const { inputType, label } = getInputDetails();

  return (
    <>
      <div className="relative flex flex-col md:flex-row w-full">
        {/* Mobile Banner */}
        <div className="block md:hidden w-full">
          {data.login_page_banner && (
            <img
              className="w-full h-[220px] sm:h-[250px] object-cover"
              src={data.login_page_banner}
              alt="background"
            />
          )}
        </div>

        {/* Right Column (Form) */}
        <div className="w-[25%] bg-white flex flex-col justify-center relative px-4 py-8 text-center md:absolute md:top-0 md:right-0 md:min-h-screen">
          {/* Powered By Logo */}
          <div className="absolute flex flex-row !space-x-2 right-10 top-6">
            <span className="h-[25px] text-xs flex justify-center items-center text-[#aaa]">
              Powered By
            </span>
            <img
              src="./images/rg.png"
              height={25}
              alt="powered-by"
              className="h-[25px]"
            />
          </div>

          {/* Form Container */}
          <div className="relative">
            <div className="text-left mb-0 !pl-14">
              <img width="55" src="./images/2883833.png" alt="icon" />
            </div>

            <form onSubmit={handleSubmit} className="w-full !py-4 !px-12">
              {/* Voucher Code Field */}
              <div className="relative !p-4">
                <input
                  type="text"
                  id="VoucherCode"
                  className="form-field-custom"
                  value={voucherCode}
                  onChange={(e) => setVoucherCode(e.target.value)}
                  placeholder=" "
                  autoComplete="off"
                />
                <label htmlFor="VoucherCode" className="form-label-custom">
                  Voucher Code
                </label>
              </div>

              {/* Contact Info Field */}
              <div className="relative !p-4">
                <input
                  type={inputType}
                  id="contactInfo"
                  className="form-field-custom"
                  value={contactInfo}
                  onChange={(e) => setContactInfo(e.target.value)}
                  placeholder=" "
                  autoComplete="off"
                />
                <label htmlFor="contactInfo" className="form-label-custom">
                  {label}
                </label>
              </div>

              {/* Privacy Policy Checkbox */}
              <div className="relative !px-4 !mt-2 !text-left !text-xs !text-gray-400">
                <input
                  type="checkbox"
                  id="privacyPolicy"
                  name="privacyPolicy"
                  checked={privacyPolicy}
                  onChange={() => setPrivacyPolicy(!privacyPolicy)}
                  className="!mb-2 !mr-2 !scale-110 md:!scale-120"
                />
                I agree to{" "}
                <a href="#" className="footer-link-custom">
                  privacy policy
                </a>
              </div>

              {/* Error Message */}
              {error && (
                <p className="text-[#d52b1e] text-xs text-left block !mt-2 !px-4">
                  {error}
                </p>
              )}

              {/* h-captcha hidden */}
              <div
                className="hidden text-center mt-10"
                data-sitekey="194e8efb-f32d-4d44-8ffb-b54694c2f321"
              ></div>

              {/* Submit Button */}
              <button
                type="submit"
                className="!h-[38px] !text-[14px] py-0 !px-[30px] !bg-[#555] !mt-[40px] !border-none !w-auto !rounded-[30px] !text-white !cursor-pointer !transition-all !duration-300 hover:!bg-[#d66159]"
                id="Signin"
              >
                Submit
              </button>
            </form>

            {/* Footer Links */}
            <div className="flex w-full justify-between !px-12 !-mt-8 text-center">
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-[#d52b1e] hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                  <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                </svg>
                <span className="sm:text-[10px] sm:mt-1">User Guide</span>
              </a>
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                <span className="sm:text-[10px] sm:mt-1">
                  Add To Home Screen
                </span>
              </a>
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 25 25"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <span className="sm:text-[10px] sm:mt-1">Contact Us</span>
              </a>
            </div>
          </div>
        </div>

        {/* Left Column (Banner/Background) - Desktop Only */}
        <div
          className="hidden md:block flex-1 min-h-screen relative text-center bg-cover bg-repeat bg-center max-w-[75%]"
          style={{
            backgroundImage: `url(${
              data?.login_page_background || "/images/background.png"
            })`,
            backgroundColor: "#cccccc",
            backgroundSize: "500px 500px",
          }}
        >
          {data.login_page_banner && (
            <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-[-50%] m-0">
              <img
                className="shadow-lg rounded-tl-2xl rounded-bl-2xl object-cover h-[85vh] w-[75%]"
                style={{ width: "155%" }}
                src={data.login_page_banner}
                alt="overlay background"
              />
            </div>
          )}
        </div>


               <div
          className="hidden md:flex min-h-screen relative items-center justify-center bg-cover bg-repeat bg-center w-1/2"
          style={{
            backgroundImage: `url(${data?.login_page_background || "/images/background.png"})`,
            backgroundColor: "#cccccc",
            backgroundSize: "500px 500px",
          }}
        >
          {data.login_page_banner && (
            <div className="flex items-center justify-center w-full">
              <img
                className="mx-auto shadow-lg rounded-tl-2xl rounded-bl-2xl object-cover h-[85vh] w-1/2"
                src={data.login_page_banner}
                alt="overlay background"
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
